2025-07-12 03:59:52,429 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 03:59:52,430 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 03:59:52,434 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 03:59:52,435 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 03:59:52,436 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 03:59:52,437 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 03:59:56,900 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:00:01,777 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Started bot
2025-07-12 04:00:01,778 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:08,605 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:00:08,606 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:18,524 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed educational content section
2025-07-12 04:00:18,526 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:25,970 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed ministerial materials section
2025-07-12 04:00:25,972 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:31,495 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed services section
2025-07-12 04:00:31,496 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:39,174 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed services section
2025-07-12 04:00:39,177 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:40,558 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:00:40,560 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:01:07,874 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:02:52,746 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:02:52,746 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:02:52,748 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:02:52,749 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:02:52,751 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:02:52,752 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:02:53,811 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:02:57,716 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:02:57,718 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:03:49,168 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:03:49,170 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:03:55,597 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:04:39,037 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:04:39,038 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:04:39,039 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:04:39,039 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:04:39,040 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:04:39,040 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:04:40,028 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:04:57,487 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:04:57,488 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:05:41,227 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:05:41,230 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:05:41,233 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:05:41,233 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:05:41,234 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:05:41,235 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:05:42,222 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:05:43,250 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:05:43,252 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:05:46,644 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:05:46,651 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:05:54,519 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:05:56,722 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: 404 No document to update: projects/rbot-e906c/databases/(default)/documents/users/5445116367
2025-07-12 04:06:21,613 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:06:21,614 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:06:21,616 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:06:21,617 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:06:21,617 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:06:21,617 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:06:22,551 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:06:29,362 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:06:30,963 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: 404 No document to update: projects/rbot-e906c/databases/(default)/documents/users/5445116367
2025-07-12 04:06:47,909 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:11:56,163 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:11:56,166 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:11:56,166 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:11:56,167 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:11:56,167 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:11:57,388 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:16:11,475 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:16:11,477 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:16:11,477 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:16:11,477 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:16:11,478 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:16:12,541 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:16:17,087 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:16:21,281 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:16:29,971 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:19:46,104 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:19:46,115 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:19:46,116 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:19:46,116 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:19:46,116 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:19:47,206 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:21:49,865 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:21:49,867 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:21:49,867 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:21:49,867 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:21:49,867 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:21:50,951 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:22:00,757 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:24:17,236 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed ministerial materials section
2025-07-12 04:25:31,964 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed ministerial materials section
2025-07-12 04:26:02,315 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed ministerial materials section
2025-07-12 04:26:23,494 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:26:23,497 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:26:23,497 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:26:23,497 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:26:23,498 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:26:24,784 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:26:26,132 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:26,135 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:29,515 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:29,517 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:29,930 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:29,932 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:31,186 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed services section
2025-07-12 04:26:33,586 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:33,590 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:34,223 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:34,225 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:35,379 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 262
2025-07-12 04:26:37,603 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:37,605 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:38,723 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:38,728 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:42,134 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:42,136 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:44,270 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:26:44,273 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:26:51,052 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 262
2025-07-12 04:26:54,060 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 268
2025-07-12 04:26:56,224 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 283
2025-07-12 04:26:57,809 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 306
2025-07-12 04:27:05,782 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed educational content section
2025-07-12 04:27:10,455 - __main__ - ERROR - خطأ في معالجة callback query: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: BUTTON_DATA_INVALID
2025-07-12 04:27:43,065 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:27:43,066 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:27:43,066 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:27:43,067 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:27:43,067 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:27:44,103 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:27:50,053 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:28:38,317 - firebase_manager - INFO - ✅ تم حفظ الفكرة: 0QcgGtlj6Cnj9MvnKS3q
2025-07-12 04:29:59,687 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:30:21,260 - firebase_manager - INFO - ✅ تم حفظ الفكرة: fKif5U28vdxUiwb51Gfq
2025-07-12 04:39:02,373 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:41:21,201 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:41:22,654 - firebase_manager - INFO - ✅ تم حفظ المحتوى التعليمي
2025-07-12 04:41:24,039 - firebase_manager - INFO - ✅ تم حفظ الأسئلة الوزارية
2025-07-12 04:41:25,450 - firebase_manager - INFO - ✅ تم حفظ بيانات الخدمات
2025-07-12 04:41:25,450 - __main__ - INFO - ✅ تم تحميل البيانات من Firebase بنجاح
2025-07-12 04:41:25,451 - __main__ - INFO - 🚀 بدء تشغيل البوت التعليمي المحسن...
2025-07-12 04:41:25,452 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:41:25,452 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:41:25,453 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:41:25,454 - __main__ - INFO - 👨‍💼 تم تحميل 1 مشرف
2025-07-12 04:41:26,460 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:41:27,636 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:27,645 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:31,032 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:31,035 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:31,402 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:31,412 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:35,057 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:35,066 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:35,716 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:35,726 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:39,874 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:39,885 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:41:41,005 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:41:41,009 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:43:14,711 - firebase_manager - INFO - 📊 تم تحميل 1 مشرف من Firestore
2025-07-12 04:43:16,572 - __main__ - INFO - ✅ تم تحميل البيانات من Firebase بنجاح
2025-07-12 04:43:16,572 - __main__ - INFO - 🚀 بدء تشغيل البوت التعليمي المحسن...
2025-07-12 04:43:16,572 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:43:16,573 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:43:16,573 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:43:16,573 - __main__ - INFO - 👨‍💼 تم تحميل 1 مشرف
2025-07-12 04:43:17,706 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:43:23,177 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:43:31,090 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed admin control panel
2025-07-12 04:43:46,158 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed admin control panel
2025-07-12 04:44:16,630 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed admin control panel
2025-07-12 04:45:07,212 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed admin control panel
2025-07-12 04:46:26,981 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed admin control panel
