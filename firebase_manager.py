#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير Firebase للبوت التعليمي
Firebase Manager for Educational Bot

يدير الاتصال مع Firebase Realtime Database و Firestore
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import firebase_admin
from firebase_admin import credentials, firestore, db
import pyrebase

logger = logging.getLogger(__name__)

class FirebaseManager:
    """مدير Firebase للبوت التعليمي"""
    
    def __init__(self):
        self.db_client = None
        self.realtime_db = None
        self.pyrebase_app = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            # إنشاء ملف الاعتماد من متغيرات البيئة
            firebase_config = self._create_firebase_config()
            
            # تهيئة Firebase Admin SDK
            if not firebase_admin._apps:
                cred = credentials.Certificate(firebase_config)
                firebase_admin.initialize_app(cred, {
                    'databaseURL': os.getenv('FIREBASE_DATABASE_URL')
                })
            
            # تهيئة Firestore
            self.db_client = firestore.client()
            
            # تهيئة Realtime Database
            self.realtime_db = db.reference()
            
            # تهيئة Pyrebase للمميزات الإضافية
            pyrebase_config = {
                "apiKey": "your-api-key",  # يمكن الحصول عليه من Firebase Console
                "authDomain": f"{os.getenv('FIREBASE_PROJECT_ID')}.firebaseapp.com",
                "databaseURL": os.getenv('FIREBASE_DATABASE_URL'),
                "projectId": os.getenv('FIREBASE_PROJECT_ID'),
                "storageBucket": f"{os.getenv('FIREBASE_PROJECT_ID')}.appspot.com",
                "messagingSenderId": "123456789",
                "appId": "1:123456789:web:abcdef123456"
            }
            
            self.pyrebase_app = pyrebase.initialize_app(pyrebase_config)
            
            logger.info("✅ تم تهيئة Firebase بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Firebase: {e}")
            raise
    
    def _create_firebase_config(self) -> Dict[str, str]:
        """إنشاء ملف الاعتماد من متغيرات البيئة"""
        return {
            "type": os.getenv('FIREBASE_TYPE'),
            "project_id": os.getenv('FIREBASE_PROJECT_ID'),
            "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
            "private_key": os.getenv('FIREBASE_PRIVATE_KEY').replace('\\n', '\n'),
            "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
            "client_id": os.getenv('FIREBASE_CLIENT_ID'),
            "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
            "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
            "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
            "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
            "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN')
        }
    
    # ==================== إدارة المشرفين ====================
    
    def get_admin_ids(self) -> List[int]:
        """الحصول على قائمة معرفات المشرفين من Firestore"""
        try:
            admins_ref = self.db_client.collection('admins')
            docs = admins_ref.stream()
            
            admin_ids = []
            for doc in docs:
                data = doc.to_dict()
                if data.get('active', True):
                    admin_ids.append(data.get('telegram_id'))
            
            logger.info(f"📊 تم تحميل {len(admin_ids)} مشرف من Firestore")
            return admin_ids
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المشرفين: {e}")
            return []
    
    def add_admin(self, telegram_id: int, admin_data: Dict[str, Any]) -> bool:
        """إضافة مشرف جديد"""
        try:
            self.db_client.collection('admins').document(str(telegram_id)).set(admin_data)
            logger.info(f"✅ تم إضافة المشرف: {admin_data.get('name', telegram_id)} ({telegram_id})")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المشرف: {e}")
            return False
    
    def remove_admin(self, telegram_id: int) -> bool:
        """إزالة مشرف"""
        try:
            self.db_client.collection('admins').document(str(telegram_id)).update({
                'active': False,
                'removed_date': datetime.now()
            })
            logger.info(f"✅ تم إزالة المشرف: {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إزالة المشرف: {e}")
            return False

    def get_admin_details(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل مشرف محدد"""
        try:
            doc = self.db_client.collection('admins').document(str(telegram_id)).get()
            if doc.exists:
                return doc.to_dict()
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تفاصيل المشرف {telegram_id}: {e}")
            return None

    def update_admin_info(self, telegram_id: int, update_data: Dict[str, Any]) -> bool:
        """تحديث معلومات المشرف"""
        try:
            self.db_client.collection('admins').document(str(telegram_id)).update(update_data)
            logger.info(f"✅ تم تحديث معلومات المشرف: {telegram_id}")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث معلومات المشرف {telegram_id}: {e}")
            return False

    # ==================== إدارة المستخدمين ====================
    
    def save_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """حفظ بيانات المستخدم في Firestore"""
        try:
            user_data['last_update'] = datetime.now()
            self.db_client.collection('users').document(str(user_id)).set(user_data, merge=True)
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ بيانات المستخدم {user_id}: {e}")
            return False
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات المستخدم من Firestore"""
        try:
            doc = self.db_client.collection('users').document(str(user_id)).get()
            if doc.exists:
                return doc.to_dict()
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
            return None
    
    def update_user_activity(self, user_id: int, action: str) -> bool:
        """تحديث نشاط المستخدم"""
        try:
            current_time = datetime.now()
            activity_data = {
                'user_id': user_id,
                'action': action,
                'timestamp': current_time.isoformat()  # تحويل إلى نص
            }

            # حفظ في Realtime Database للسرعة
            self.realtime_db.child('user_activities').child(str(user_id)).push(activity_data)

            # تحديث آخر نشاط في Firestore (أو إنشاء المستند إذا لم يكن موجوداً)
            user_doc_ref = self.db_client.collection('users').document(str(user_id))
            user_doc = user_doc_ref.get()

            if user_doc.exists:
                # تحديث المستند الموجود
                user_doc_ref.update({
                    'last_activity': current_time,
                    'last_action': action
                })
            else:
                # إنشاء مستند جديد
                user_doc_ref.set({
                    'user_id': user_id,
                    'created_date': current_time,
                    'last_activity': current_time,
                    'last_action': action,
                    'active': True
                })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث نشاط المستخدم {user_id}: {e}")
            return False
    
    # ==================== إدارة الأفكار المقترحة ====================
    
    def save_idea(self, idea_data: Dict[str, Any]) -> Optional[str]:
        """حفظ فكرة مقترحة في Firestore"""
        try:
            idea_data['created_date'] = datetime.now()
            idea_data['status'] = 'جديدة'

            # إضافة الفكرة والحصول على المرجع
            _, doc_ref = self.db_client.collection('suggested_ideas').add(idea_data)
            idea_id = doc_ref.id

            logger.info(f"✅ تم حفظ الفكرة: {idea_id}")
            return idea_id

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفكرة: {e}")
            return None
    
    def get_ideas(self, limit: int = 10) -> List[Dict[str, Any]]:
        """الحصول على الأفكار المقترحة"""
        try:
            ideas_ref = self.db_client.collection('suggested_ideas')
            docs = ideas_ref.order_by('created_date', direction=firestore.Query.DESCENDING).limit(limit).stream()
            
            ideas = []
            for doc in docs:
                idea_data = doc.to_dict()
                idea_data['id'] = doc.id
                ideas.append(idea_data)
            
            return ideas
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الأفكار: {e}")
            return []
    
    def update_idea_status(self, idea_id: str, status: str, admin_id: int) -> bool:
        """تحديث حالة الفكرة"""
        try:
            self.db_client.collection('suggested_ideas').document(idea_id).update({
                'status': status,
                'reviewed_by': admin_id,
                'reviewed_date': datetime.now()
            })
            
            logger.info(f"✅ تم تحديث حالة الفكرة {idea_id} إلى {status}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة الفكرة: {e}")
            return False
    
    # ==================== إدارة نتائج الاختبارات ====================
    
    def save_test_result(self, user_id: int, test_data: Dict[str, Any]) -> bool:
        """حفظ نتيجة الاختبار"""
        try:
            test_data['user_id'] = user_id
            test_data['date'] = datetime.now()
            
            self.db_client.collection('test_results').add(test_data)
            
            # تحديث إحصائيات المستخدم
            self.update_user_stats(user_id, test_data)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ نتيجة الاختبار: {e}")
            return False
    
    def get_user_test_results(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """الحصول على نتائج اختبارات المستخدم"""
        try:
            results_ref = self.db_client.collection('test_results')
            docs = results_ref.where('user_id', '==', user_id).order_by('date', direction=firestore.Query.DESCENDING).limit(limit).stream()
            
            results = []
            for doc in docs:
                result_data = doc.to_dict()
                result_data['id'] = doc.id
                results.append(result_data)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على نتائج الاختبارات: {e}")
            return []
    
    def update_user_stats(self, user_id: int, test_data: Dict[str, Any]) -> bool:
        """تحديث إحصائيات المستخدم"""
        try:
            user_ref = self.db_client.collection('users').document(str(user_id))
            user_doc = user_ref.get()
            
            if user_doc.exists:
                user_data = user_doc.to_dict()
                stats = user_data.get('stats', {})
                
                # تحديث الإحصائيات
                stats['total_tests'] = stats.get('total_tests', 0) + 1
                stats['total_score'] = stats.get('total_score', 0) + test_data.get('score', 0)
                stats['average_score'] = stats['total_score'] / stats['total_tests']
                
                if test_data.get('score', 0) > stats.get('best_score', 0):
                    stats['best_score'] = test_data.get('score', 0)
                    stats['best_subject'] = test_data.get('subject', '')
                
                user_ref.update({'stats': stats})
                
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث إحصائيات المستخدم: {e}")
            return False

    # ==================== إدارة المحتوى التعليمي ====================

    def save_educational_content(self, content_data: Dict[str, Any]) -> bool:
        """حفظ المحتوى التعليمي في Firestore"""
        try:
            self.db_client.collection('educational_content').document('main_content').set(content_data)
            logger.info("✅ تم حفظ المحتوى التعليمي")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ المحتوى التعليمي: {e}")
            return False

    def get_educational_content(self) -> Dict[str, Any]:
        """الحصول على المحتوى التعليمي من Firestore"""
        try:
            doc = self.db_client.collection('educational_content').document('main_content').get()
            if doc.exists:
                return doc.to_dict()
            return {}

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المحتوى التعليمي: {e}")
            return {}

    def save_ministerial_questions(self, questions_data: Dict[str, Any]) -> bool:
        """حفظ الأسئلة الوزارية في Firestore"""
        try:
            self.db_client.collection('ministerial_questions').document('main_questions').set(questions_data)
            logger.info("✅ تم حفظ الأسئلة الوزارية")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الأسئلة الوزارية: {e}")
            return False

    def get_ministerial_questions(self) -> Dict[str, Any]:
        """الحصول على الأسئلة الوزارية من Firestore"""
        try:
            doc = self.db_client.collection('ministerial_questions').document('main_questions').get()
            if doc.exists:
                return doc.to_dict()
            return {}

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الأسئلة الوزارية: {e}")
            return {}

    def save_services_data(self, services_data: Dict[str, Any]) -> bool:
        """حفظ بيانات الخدمات في Firestore"""
        try:
            self.db_client.collection('services').document('main_services').set(services_data)
            logger.info("✅ تم حفظ بيانات الخدمات")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ بيانات الخدمات: {e}")
            return False

    def get_services_data(self) -> Dict[str, Any]:
        """الحصول على بيانات الخدمات من Firestore"""
        try:
            doc = self.db_client.collection('services').document('main_services').get()
            if doc.exists:
                return doc.to_dict()
            return {}

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات الخدمات: {e}")
            return {}

    # ==================== إدارة الإحصائيات ====================

    def get_bot_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات البوت"""
        try:
            stats = {}

            # عدد المستخدمين
            users_count = len(list(self.db_client.collection('users').stream()))
            stats['total_users'] = users_count

            # عدد الأفكار المقترحة
            ideas_count = len(list(self.db_client.collection('suggested_ideas').stream()))
            stats['total_ideas'] = ideas_count

            # عدد الاختبارات
            tests_count = len(list(self.db_client.collection('test_results').stream()))
            stats['total_tests'] = tests_count

            # عدد المشرفين النشطين
            active_admins = len([doc for doc in self.db_client.collection('admins').stream()
                               if doc.to_dict().get('active', False)])
            stats['active_admins'] = active_admins

            # آخر تحديث
            stats['last_update'] = datetime.now()

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإحصائيات: {e}")
            return {}

    def log_bot_activity(self, activity_type: str, details: Dict[str, Any]) -> bool:
        """تسجيل نشاط البوت في Realtime Database"""
        try:
            activity_data = {
                'type': activity_type,
                'details': details,
                'timestamp': datetime.now().isoformat()
            }

            self.realtime_db.child('bot_activities').push(activity_data)
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل نشاط البوت: {e}")
            return False

    # ==================== إدارة الملفات والترجمة ====================

    def save_translation_request(self, user_id: int, request_data: Dict[str, Any]) -> Optional[str]:
        """حفظ طلب ترجمة"""
        try:
            request_data['user_id'] = user_id
            request_data['created_date'] = datetime.now()
            request_data['status'] = 'pending'

            # إضافة طلب الترجمة والحصول على المرجع
            _, doc_ref = self.db_client.collection('translation_requests').add(request_data)
            request_id = doc_ref.id

            logger.info(f"✅ تم حفظ طلب الترجمة: {request_id}")
            return request_id

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ طلب الترجمة: {e}")
            return None

    def update_translation_status(self, request_id: str, status: str, result: str = None) -> bool:
        """تحديث حالة طلب الترجمة"""
        try:
            update_data = {
                'status': status,
                'updated_date': datetime.now()
            }

            if result:
                update_data['result'] = result

            self.db_client.collection('translation_requests').document(request_id).update(update_data)
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة الترجمة: {e}")
            return False

    # ==================== وظائف مساعدة ====================

    def backup_data(self) -> bool:
        """نسخ احتياطي للبيانات"""
        try:
            backup_data = {
                'users': [doc.to_dict() for doc in self.db_client.collection('users').stream()],
                'admins': [doc.to_dict() for doc in self.db_client.collection('admins').stream()],
                'ideas': [doc.to_dict() for doc in self.db_client.collection('suggested_ideas').stream()],
                'test_results': [doc.to_dict() for doc in self.db_client.collection('test_results').stream()],
                'backup_date': datetime.now().isoformat()
            }

            # حفظ النسخة الاحتياطية
            self.db_client.collection('backups').add(backup_data)

            logger.info("✅ تم إنشاء نسخة احتياطية من البيانات")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

    def cleanup_old_data(self, days: int = 30) -> bool:
        """تنظيف البيانات القديمة"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)

            # حذف الأنشطة القديمة من Realtime Database
            activities_ref = self.realtime_db.child('user_activities')
            # يمكن إضافة منطق حذف البيانات القديمة هنا

            logger.info(f"✅ تم تنظيف البيانات الأقدم من {days} يوم")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف البيانات: {e}")
            return False

    def get_all_users(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المستخدمين"""
        try:
            users = []
            docs = self.db_client.collection('users').stream()

            for doc in docs:
                user_data = doc.to_dict()
                user_data['id'] = doc.id
                users.append(user_data)

            return users

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين: {e}")
            return []

    def close_connections(self):
        """إغلاق الاتصالات"""
        try:
            # Firebase Admin SDK يدير الاتصالات تلقائياً
            logger.info("✅ تم إغلاق اتصالات Firebase")

        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق الاتصالات: {e}")

# إنشاء مثيل مدير Firebase
firebase_manager = FirebaseManager()
