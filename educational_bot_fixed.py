#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 بوت تيليجرام تعليمي متعدد الأقسام - نسخة محسنة
Educational Telegram Bot with Enhanced Admin Panel

المطور: كرار الحدراوي
التاريخ: 2025-07-12
الوصف: بوت تعليمي احترافي مع لوحة تحكم شاملة للمشرفين
"""

import telebot
from telebot import types
import os
import logging
from datetime import datetime
import time
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# استيراد مدير Firebase
from firebase_manager import firebase_manager

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت من متغيرات البيئة
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# الحصول على معرفات المشرفين من Firebase
try:
    ADMIN_IDS = firebase_manager.get_admin_ids()
    if not ADMIN_IDS:
        # إذا لم توجد معرفات في Firebase، استخدم القيم الافتراضية
        ADMIN_IDS = [123456789]
        logger.warning("⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية")
except Exception as e:
    logger.error(f"❌ خطأ في الحصول على المشرفين من Firebase: {e}")
    ADMIN_IDS = [123456789]

# إنشاء كائن البوت
bot = telebot.TeleBot(BOT_TOKEN)

# قاموس لحفظ حالات المستخدمين (مؤقت في الذاكرة)
user_states = {}

# تحميل البيانات من Firebase
def load_data_from_firebase():
    """تحميل البيانات من Firebase"""
    global educational_content, ministerial_questions, services_data

    try:
        # تحميل المحتوى التعليمي
        educational_content = firebase_manager.get_educational_content()
        if not educational_content:
            educational_content = get_default_educational_content()
            firebase_manager.save_educational_content(educational_content)

        # تحميل الأسئلة الوزارية
        ministerial_questions = firebase_manager.get_ministerial_questions()
        if not ministerial_questions:
            ministerial_questions = get_default_ministerial_questions()
            firebase_manager.save_ministerial_questions(ministerial_questions)

        # تحميل بيانات الخدمات
        services_data = firebase_manager.get_services_data()
        if not services_data:
            services_data = get_default_services_data()
            firebase_manager.save_services_data(services_data)

        logger.info("✅ تم تحميل البيانات من Firebase بنجاح")

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل البيانات من Firebase: {e}")
        # استخدام البيانات الافتراضية في حالة الخطأ
        educational_content = get_default_educational_content()
        ministerial_questions = get_default_ministerial_questions()
        services_data = get_default_services_data()

def get_default_educational_content():
    """الحصول على المحتوى التعليمي الافتراضي"""
    return {
        "المرحلة الأولى": {
            "الفيزياء الطبية": {
                "books": ["كتاب الفيزياء الطبية الأساسية", "مبادئ الإشعاع الطبي"],
                "resources": ["ملزمة الفيزياء النووية", "دليل الأمان الإشعاعي"],
                "status": "متوفر"
            },
            "التشريح": {
                "books": ["أطلس التشريح البشري", "علم التشريح التطبيقي"],
                "resources": ["ملزمة التشريح المصورة", "نماذج ثلاثية الأبعاد"],
                "status": "متوفر"
            }
        },
        "المرحلة الثانية": {
            "تقنيات التصوير الطبي": {
                "books": ["أساسيات التصوير الإشعاعي", "تقنيات الأشعة المقطعية"],
                "resources": ["ملزمة التصوير بالرنين المغناطيسي", "حالات سريرية"],
                "status": "متوفر"
            },
            "علم الأمراض": {
                "books": ["علم الأمراض العام", "الأمراض الإشعاعية"],
                "resources": ["أطلس الأمراض المصور", "دراسات حالة"],
                "status": "متوفر"
            }
        },
        "المرحلة الثالثة": {
            "التصوير التشخيصي المتقدم": {
                "books": ["التصوير الطبي المتقدم", "تفسير الصور الإشعاعية"],
                "resources": ["ملزمة التشخيص الإشعاعي", "حالات معقدة"],
                "status": "متوفر"
            },
            "الطب النووي": {
                "books": ["أساسيات الطب النووي", "تطبيقات النظائر المشعة"],
                "resources": ["ملزمة الفحوصات النووية", "بروتوكولات العمل"],
                "status": "متوفر"
            }
        },
        "المرحلة الرابعة": {
            "التدريب السريري": {
                "books": ["دليل التدريب السريري", "إجراءات المستشفى"],
                "resources": ["ملزمة الحالات السريرية", "تقارير التدريب"],
                "status": "متوفر"
            },
            "مشروع التخرج": {
                "books": ["منهجية البحث العلمي", "كتابة الأطروحات"],
                "resources": ["نماذج مشاريع التخرج", "دليل التوثيق"],
                "status": "متوفر"
            }
        }
    }

def get_default_ministerial_questions():
    """الحصول على الأسئلة الوزارية الافتراضية"""
    return {
        "الفيزياء الطبية": {
            "2023": {
                "questions": [
                    {
                        "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                        "options": ["انبعاث الإلكترونات", "التأين الإشعاعي", "الرنين المغناطيسي", "الموجات فوق الصوتية"],
                        "correct": 0,
                        "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية"
                    },
                    {
                        "question": "ما هي وحدة قياس الجرعة الإشعاعية؟",
                        "options": ["جراي (Gy)", "سيفرت (Sv)", "بيكريل (Bq)", "كوري (Ci)"],
                        "correct": 0,
                        "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة"
                    }
                ],
                "pdf_link": "https://example.com/physics_2023.pdf"
            }
        }
    }

def get_default_services_data():
    """الحصول على بيانات الخدمات الافتراضية"""
    return {
        "التصميم": {
            "description": "خدمات التصميم الجرافيكي والإبداعي",
            "specialists": ["أحمد محمد - مصمم جرافيك", "فاطمة علي - مصممة UI/UX"],
            "contact": "@design_team"
        },
        "البرمجة": {
            "description": "تطوير المواقع والتطبيقات والأنظمة",
            "specialists": ["محمد أحمد - مطور ويب", "سارة حسن - مطورة تطبيقات"],
            "contact": "@programming_team"
        },
        "كتابة المقالات": {
            "description": "كتابة المحتوى والمقالات العلمية والأدبية",
            "specialists": ["نور الدين - كاتب محتوى", "ليلى محمود - محررة"],
            "contact": "@writing_team"
        },
        "إعداد البحوث العلمية": {
            "description": "إعداد وتنسيق البحوث والدراسات العلمية",
            "specialists": ["د. عمر سالم - باحث أكاديمي", "د. هدى كريم - محررة علمية"],
            "contact": "@research_team"
        }
    }

class UserStates:
    """فئة لتعريف حالات المستخدمين"""
    MAIN_MENU = "main_menu"
    TRANSLATION = "translation"
    EDUCATIONAL_CONTENT = "educational_content"
    MINISTERIAL_MATERIALS = "ministerial_materials"
    SERVICES = "services"
    SUGGEST_IDEA = "suggest_idea"
    WAITING_FOR_TEXT = "waiting_for_text"
    WAITING_FOR_FILE = "waiting_for_file"
    WAITING_FOR_IDEA_NAME = "waiting_for_idea_name"
    WAITING_FOR_IDEA_AUTHOR = "waiting_for_idea_author"
    WAITING_FOR_IDEA_DESCRIPTION = "waiting_for_idea_description"
    WAITING_FOR_IDEA_CATEGORY = "waiting_for_idea_category"
    # حالات إدارية جديدة
    ADMIN_BROADCAST = "admin_broadcast"
    ADMIN_ADD_ADMIN = "admin_add_admin"
    ADMIN_REMOVE_ADMIN = "admin_remove_admin"

def create_main_keyboard(user_id=None):
    """إنشاء الكيبورد الرئيسي"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
    
    # الأزرار الرئيسية
    btn_translation = types.KeyboardButton("📝 الترجمة")
    btn_content = types.KeyboardButton("📖 المحتوى العلمي")
    btn_ministerial = types.KeyboardButton("🗂️ المواد الوزارية")
    btn_services = types.KeyboardButton("🛠️ الخدمات")
    btn_suggest = types.KeyboardButton("💡 اقتراح فكرة جديدة")
    
    keyboard.add(btn_translation, btn_content)
    keyboard.add(btn_ministerial, btn_services)
    keyboard.add(btn_suggest)
    
    # إضافة زر لوحة التحكم للمشرفين فقط
    if user_id and user_id in ADMIN_IDS:
        btn_admin = types.KeyboardButton("🔧 لوحة التحكم")
        keyboard.add(btn_admin)
    
    return keyboard

def create_back_keyboard():
    """إنشاء كيبورد العودة للقائمة الرئيسية"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
    btn_back = types.KeyboardButton("🔙 العودة للقائمة الرئيسية")
    keyboard.add(btn_back)
    return keyboard

def get_user_state(user_id):
    """الحصول على حالة المستخدم"""
    return user_states.get(user_id, UserStates.MAIN_MENU)

def set_user_state(user_id, state):
    """تعيين حالة المستخدم"""
    user_states[user_id] = state

def get_user_data(user_id):
    """الحصول على بيانات المستخدم من Firebase"""
    try:
        user_data = firebase_manager.get_user(user_id)
        if user_data is None:
            user_data = {}
        # التأكد من وجود المفاتيح الأساسية
        if 'user_id' not in user_data:
            user_data['user_id'] = user_id
        return user_data
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
        return {'user_id': user_id}

def log_user_action(user_id, username, action):
    """تسجيل إجراءات المستخدمين"""
    logger.info(f"User {user_id} (@{username}) performed action: {action}")

    # حفظ النشاط في Firebase
    try:
        firebase_manager.update_user_activity(user_id, action)
    except Exception as e:
        logger.error(f"خطأ في حفظ نشاط المستخدم: {e}")

def save_user_data(user_id, data):
    """حفظ بيانات المستخدم في Firebase"""
    try:
        firebase_manager.save_user(user_id, data)
    except Exception as e:
        logger.error(f"خطأ في حفظ بيانات المستخدم {user_id}: {e}")

# تحميل البيانات عند بدء التشغيل
load_data_from_firebase()

@bot.message_handler(commands=['start'])
def start_command(message):
    """معالج أمر البدء"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    # تحديث معلومات المشرف إذا كان مشرفاً
    if user_id in ADMIN_IDS:
        user_info = {
            'username': message.from_user.username,
            'first_name': message.from_user.first_name,
            'last_name': message.from_user.last_name,
            'language_code': message.from_user.language_code
        }
        try:
            firebase_manager.update_admin_info(user_id, user_info)
        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات المشرف {user_id}: {e}")

    log_user_action(user_id, username, "Started bot")
    set_user_state(user_id, UserStates.MAIN_MENU)

    welcome_text = """
🎓 **أهلاً وسهلاً بك في البوت التعليمي المتطور!**

مرحباً بك في منصتك التعليمية الشاملة التي تضم:

📝 **الترجمة** - ترجمة الملفات والنصوص من الإنجليزية للعربية
📖 **المحتوى العلمي** - مواد دراسية منظمة حسب المرحلة
🗂️ **المواد الوزارية** - أسئلة وزارية مع الحلول واختبارات تفاعلية
🛠️ **الخدمات** - خدمات متنوعة من فريق المنصة
💡 **اقتراح فكرة جديدة** - شاركنا أفكارك الإبداعية
"""

    # إضافة رسالة خاصة للمشرفين
    if user_id in ADMIN_IDS:
        welcome_text += "\n🔧 **لوحة التحكم** - إدارة شاملة للبوت (متاحة للمشرفين فقط)\n"

    welcome_text += "\nاختر القسم الذي تريده من الأزرار أدناه ⬇️"

    bot.send_message(
        message.chat.id,
        welcome_text,
        parse_mode='Markdown',
        reply_markup=create_main_keyboard(user_id)
    )

@bot.message_handler(func=lambda message: message.text == "🔧 لوحة التحكم")
def admin_control_panel(message):
    """لوحة التحكم الرئيسية للمشرفين"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    # التحقق من صلاحيات الإدارة
    if user_id not in ADMIN_IDS:
        bot.send_message(
            message.chat.id,
            "❌ عذراً، هذه الميزة متاحة للمشرفين فقط."
        )
        return

    log_user_action(user_id, username, "Accessed admin control panel")

    keyboard = types.InlineKeyboardMarkup(row_width=2)

    # الأزرار الإدارية الرئيسية
    btn_stats = types.InlineKeyboardButton("📊 الإحصائيات", callback_data="admin_stats")
    btn_users = types.InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users")
    btn_ideas = types.InlineKeyboardButton("💡 الأفكار المقترحة", callback_data="admin_ideas")
    btn_content = types.InlineKeyboardButton("📚 إدارة المحتوى", callback_data="admin_content")
    btn_admins = types.InlineKeyboardButton("👨‍💼 إدارة المشرفين", callback_data="admin_admins")
    btn_broadcast = types.InlineKeyboardButton("📢 رسالة جماعية", callback_data="admin_broadcast")
    btn_backup = types.InlineKeyboardButton("💾 النسخ الاحتياطي", callback_data="admin_backup")
    btn_settings = types.InlineKeyboardButton("⚙️ إعدادات البوت", callback_data="admin_settings")
    btn_logs = types.InlineKeyboardButton("📋 سجلات النشاط", callback_data="admin_logs")
    btn_maintenance = types.InlineKeyboardButton("🔧 صيانة النظام", callback_data="admin_maintenance")

    keyboard.add(btn_stats, btn_users)
    keyboard.add(btn_ideas, btn_content)
    keyboard.add(btn_admins, btn_broadcast)
    keyboard.add(btn_backup, btn_settings)
    keyboard.add(btn_logs, btn_maintenance)

    admin_text = f"""
🔧 **لوحة التحكم الإدارية**

مرحباً {message.from_user.first_name or 'المشرف'} 👋

🎛️ **لوحة التحكم الشاملة للبوت التعليمي**

📊 **الإحصائيات** - عرض إحصائيات شاملة للبوت
👥 **إدارة المستخدمين** - عرض وإدارة المستخدمين
💡 **الأفكار المقترحة** - مراجعة الأفكار الجديدة
📚 **إدارة المحتوى** - تحديث المحتوى التعليمي
👨‍💼 **إدارة المشرفين** - إضافة/إزالة المشرفين
📢 **رسالة جماعية** - إرسال رسائل لجميع المستخدمين
💾 **النسخ الاحتياطي** - إنشاء واستعادة النسخ الاحتياطية
⚙️ **إعدادات البوت** - تخصيص إعدادات البوت
📋 **سجلات النشاط** - عرض سجلات النشاط
🔧 **صيانة النظام** - أدوات الصيانة والتنظيف

اختر العملية المطلوبة:
"""

    bot.send_message(
        message.chat.id,
        admin_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "🔙 العودة للقائمة الرئيسية")
def back_to_main(message):
    """العودة للقائمة الرئيسية"""
    user_id = message.from_user.id
    set_user_state(user_id, UserStates.MAIN_MENU)

    bot.send_message(
        message.chat.id,
        "🏠 تم العودة للقائمة الرئيسية",
        reply_markup=create_main_keyboard(user_id)
    )

# معالجات الـ Callback Queries
@bot.callback_query_handler(func=lambda _: True)
def handle_callback_query(call):
    """معالج الـ callback queries"""
    user_id = call.from_user.id
    data = call.data

    # إشعار احترافي للمستخدم
    bot.answer_callback_query(call.id, "تم اختيار هذا القسم ✅")

    try:
        if data == "back_main":
            # العودة للقائمة الرئيسية
            set_user_state(user_id, UserStates.MAIN_MENU)
            bot.edit_message_text(
                "🏠 تم العودة للقائمة الرئيسية",
                call.message.chat.id,
                call.message.message_id,
                reply_markup=None
            )
            bot.send_message(
                call.message.chat.id,
                "اختر القسم المطلوب:",
                reply_markup=create_main_keyboard(user_id)
            )

        elif data.startswith("admin_"):
            # معالجة الأوامر الإدارية
            handle_admin_callback(call, data)

        elif data.startswith("category_"):
            # معالجة اختيار قسم الفكرة
            handle_idea_category(call, data)

    except Exception as e:
        logger.error(f"خطأ في معالجة callback query: {e}")
        bot.answer_callback_query(call.id, "حدث خطأ، يرجى المحاولة مرة أخرى ❌")

def handle_admin_callback(call, data):
    """معالجة الأوامر الإدارية"""
    user_id = call.from_user.id

    # التحقق من صلاحيات الإدارة
    if user_id not in ADMIN_IDS:
        bot.answer_callback_query(call.id, "❌ غير مصرح لك")
        return

    if data == "admin_stats":
        # عرض الإحصائيات من Firebase
        try:
            stats = firebase_manager.get_bot_statistics()
            stats_text = f"""
📊 **إحصائيات البوت**

👥 **المستخدمين:**
• العدد الكلي: {stats.get('total_users', 0)}
• النشطين حالياً: {len(user_states)}

💡 **الأفكار المقترحة:**
• العدد الكلي: {stats.get('total_ideas', 0)}

🎯 **الاختبارات:**
• العدد الكلي: {stats.get('total_tests', 0)}

👨‍💼 **المشرفين:**
• النشطين: {stats.get('active_admins', 0)}

📚 **المحتوى:**
• المراحل الدراسية: {len(educational_content)}
• المواد: {sum(len(stage) for stage in educational_content.values())}
• الأسئلة الوزارية: {sum(len(subject) for subject in ministerial_questions.values())}

🛠️ **الخدمات:**
• عدد الخدمات: {len(services_data)}

📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            stats_text = "❌ خطأ في تحميل الإحصائيات"

        # إضافة زر العودة
        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
        keyboard.add(btn_back)

        bot.edit_message_text(
            stats_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_ideas":
        # عرض الأفكار المقترحة
        try:
            suggested_ideas = firebase_manager.get_ideas(limit=5)
            if not suggested_ideas:
                ideas_text = "💡 لا توجد أفكار مقترحة حالياً"
            else:
                ideas_text = "💡 **الأفكار المقترحة:**\n\n"
                for idea in suggested_ideas:
                    # تحويل التاريخ إلى نص
                    created_date = idea.get('created_date', 'غير محدد')
                    if hasattr(created_date, 'strftime'):
                        created_date = created_date.strftime('%Y-%m-%d %H:%M')
                    ideas_text += f"""
🆔 **#{idea['id']}** - {idea.get('name', 'بدون اسم')}
👤 {idea.get('author', 'غير محدد')} | 📂 {idea.get('category', 'غير محدد')}
📅 {created_date}
📝 {idea.get('description', 'بدون وصف')[:100]}...
➖➖➖➖➖➖➖➖➖➖
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأفكار: {e}")
            ideas_text = "❌ خطأ في تحميل الأفكار المقترحة"

        # إضافة زر العودة
        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
        keyboard.add(btn_back)

        bot.edit_message_text(
            ideas_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_users":
        # عرض إدارة المستخدمين
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        btn_active = types.InlineKeyboardButton("👥 المستخدمين النشطين", callback_data="admin_users_active")
        btn_all = types.InlineKeyboardButton("📊 جميع المستخدمين", callback_data="admin_users_all")
        btn_banned = types.InlineKeyboardButton("🚫 المحظورين", callback_data="admin_users_banned")
        btn_stats = types.InlineKeyboardButton("📈 إحصائيات المستخدمين", callback_data="admin_users_stats")
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")

        keyboard.add(btn_active, btn_all)
        keyboard.add(btn_banned, btn_stats)
        keyboard.add(btn_back)

        users_text = """
👥 **إدارة المستخدمين**

اختر العملية المطلوبة:

👥 **المستخدمين النشطين** - عرض المستخدمين النشطين حالياً
📊 **جميع المستخدمين** - عرض قائمة بجميع المستخدمين
🚫 **المحظورين** - إدارة المستخدمين المحظورين
📈 **إحصائيات المستخدمين** - تفاصيل استخدام البوت
"""

        bot.edit_message_text(
            users_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_back":
        # العودة للوحة التحكم الرئيسية
        admin_control_panel(call.message)

    elif data == "admin_broadcast":
        # إرسال رسالة جماعية
        set_user_state(user_id, UserStates.ADMIN_BROADCAST)
        bot.edit_message_text(
            "📢 **إرسال رسالة جماعية**\n\nاكتب الرسالة التي تريد إرسالها لجميع المستخدمين:",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

    elif data == "admin_admins":
        # إدارة المشرفين
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        btn_list = types.InlineKeyboardButton("📋 قائمة المشرفين", callback_data="admin_admins_list")
        btn_add = types.InlineKeyboardButton("➕ إضافة مشرف", callback_data="admin_admins_add")
        btn_remove = types.InlineKeyboardButton("➖ إزالة مشرف", callback_data="admin_admins_remove")
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")

        keyboard.add(btn_list, btn_add)
        keyboard.add(btn_remove, btn_back)

        admins_text = """
👨‍💼 **إدارة المشرفين**

اختر العملية المطلوبة:

📋 **قائمة المشرفين** - عرض جميع المشرفين الحاليين
➕ **إضافة مشرف** - إضافة مشرف جديد للبوت
➖ **إزالة مشرف** - إزالة مشرف من البوت

⚠️ **تنبيه:** تأكد من صلاحياتك قبل إجراء أي تغييرات
"""

        bot.edit_message_text(
            admins_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_backup":
        # النسخ الاحتياطي
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        btn_create = types.InlineKeyboardButton("💾 إنشاء نسخة احتياطية", callback_data="admin_backup_create")
        btn_restore = types.InlineKeyboardButton("📥 استعادة نسخة احتياطية", callback_data="admin_backup_restore")
        btn_list = types.InlineKeyboardButton("📋 قائمة النسخ الاحتياطية", callback_data="admin_backup_list")
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")

        keyboard.add(btn_create, btn_restore)
        keyboard.add(btn_list, btn_back)

        backup_text = """
💾 **النسخ الاحتياطي**

إدارة النسخ الاحتياطية للبوت:

💾 **إنشاء نسخة احتياطية** - حفظ جميع البيانات الحالية
📥 **استعادة نسخة احتياطية** - استعادة البيانات من نسخة سابقة
📋 **قائمة النسخ الاحتياطية** - عرض جميع النسخ المتاحة

⚠️ **تنبيه:** النسخ الاحتياطي يشمل جميع بيانات المستخدمين والمحتوى
"""

        bot.edit_message_text(
            backup_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_backup_create":
        # إنشاء نسخة احتياطية
        try:
            bot.edit_message_text(
                "💾 جاري إنشاء النسخة الاحتياطية...",
                call.message.chat.id,
                call.message.message_id
            )

            success = firebase_manager.backup_data()

            if success:
                result_text = "✅ **تم إنشاء النسخة الاحتياطية بنجاح!**\n\n📅 التاريخ: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            else:
                result_text = "❌ **فشل في إنشاء النسخة الاحتياطية**\n\nيرجى المحاولة مرة أخرى أو التحقق من السجلات."

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            result_text = f"❌ **خطأ في إنشاء النسخة الاحتياطية:**\n\n`{str(e)}`"

        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للنسخ الاحتياطي", callback_data="admin_backup")
        keyboard.add(btn_back)

        bot.edit_message_text(
            result_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

def handle_idea_category(call, data):
    """معالجة اختيار قسم الفكرة"""
    user_id = call.from_user.id
    category = data.replace("category_", "")

    user_data_dict = get_user_data(user_id)
    idea_data = user_data_dict.get('idea', {})

    if category == "قسم جديد":
        # طلب اسم القسم الجديد
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)
        bot.edit_message_text(
            "📂 اكتب اسم القسم الجديد:",
            call.message.chat.id,
            call.message.message_id
        )
        return

    idea_data['category'] = category

    # حفظ الفكرة في Firebase
    idea_record = {
        'name': idea_data.get('name', ''),
        'author': idea_data.get('author', ''),
        'description': idea_data.get('description', ''),
        'category': category,
        'user_id': user_id
    }

    idea_id = firebase_manager.save_idea(idea_record)

    # إشعار المشرفين
    for admin_id in ADMIN_IDS:
        try:
            admin_text = f"""
💡 **فكرة جديدة مقترحة**

📝 **اسم الفكرة:** {idea_data.get('name', 'غير محدد')}
👤 **صاحب الفكرة:** {idea_data.get('author', 'غير محدد')}
📂 **القسم:** {category}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📋 **الوصف:**
{idea_data.get('description', 'غير محدد')}

🆔 **معرف المستخدم:** {user_id}
"""
            bot.send_message(admin_id, admin_text, parse_mode='Markdown')
        except:
            pass

    # رسالة شكر للمستخدم
    success_text = f"""
✅ **تم إرسال فكرتك بنجاح!**

📝 **ملخص الفكرة:**
• الاسم: {idea_data.get('name', 'غير محدد')}
• صاحب الفكرة: {idea_data.get('author', 'غير محدد')}
• القسم: {category}
• رقم الفكرة: #{idea_id if idea_id else 'غير محدد'}

🔔 **سيتم مراجعة فكرتك من قبل الفريق المختص**
📧 **وسيتم التواصل معك في حالة الموافقة**

شكراً لمساهمتك في تطوير المنصة! 🙏
"""

    bot.edit_message_text(
        success_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # إرسال القائمة الرئيسية
    bot.send_message(
        call.message.chat.id,
        "🏠 العودة للقائمة الرئيسية:",
        reply_markup=create_main_keyboard(user_id)
    )

    set_user_state(user_id, UserStates.MAIN_MENU)

    # تنظيف بيانات المستخدم
    if 'idea' in user_data_dict:
        del user_data_dict['idea']
        save_user_data(user_id, user_data_dict)

@bot.message_handler(func=lambda message: message.text == "💡 اقتراح فكرة جديدة")
def suggest_idea_section(message):
    """قسم اقتراح الأفكار"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed suggest idea section")
    set_user_state(user_id, UserStates.WAITING_FOR_IDEA_NAME)

    # تهيئة بيانات المستخدم
    user_data_dict = get_user_data(user_id)
    user_data_dict['idea'] = {}

    idea_text = """
💡 **اقتراح فكرة جديدة**

نحن نقدر أفكارك الإبداعية ونسعى لتطوير المنصة باستمرار!

🔹 **ما نحتاجه منك:**
• اسم الفكرة
• اسم صاحب الفكرة
• وصف مفصل للفكرة
• القسم المناسب لها

📝 **ابدأ بكتابة اسم الفكرة:**
"""

    bot.send_message(
        message.chat.id,
        idea_text,
        parse_mode='Markdown',
        reply_markup=create_back_keyboard()
    )

# معالجات الرسائل النصية
@bot.message_handler(content_types=['text'])
def handle_text_messages(message):
    """معالج الرسائل النصية"""
    user_id = message.from_user.id
    user_state = get_user_state(user_id)
    text = message.text

    if user_state == UserStates.WAITING_FOR_IDEA_NAME:
        # حفظ اسم الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['name'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_AUTHOR)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم الفكرة\n\n👤 الآن اكتب اسم صاحب الفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_AUTHOR:
        # حفظ اسم صاحب الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['author'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_DESCRIPTION)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم صاحب الفكرة\n\n📝 الآن اكتب وصف مفصل للفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_DESCRIPTION:
        # حفظ وصف الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['description'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)

        # عرض الأقسام المتاحة
        keyboard = types.InlineKeyboardMarkup(row_width=1)
        categories = ["الترجمة", "المحتوى العلمي", "المواد الوزارية", "الخدمات", "قسم جديد"]

        for category in categories:
            btn = types.InlineKeyboardButton(category, callback_data=f"category_{category}")
            keyboard.add(btn)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ وصف الفكرة\n\n📂 اختر القسم المناسب للفكرة:",
            reply_markup=keyboard
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_CATEGORY:
        # حفظ اسم القسم الجديد
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}

        category = text.strip()
        idea_data = user_data_dict['idea']

        # حفظ الفكرة في Firebase
        idea_record = {
            'name': idea_data.get('name', ''),
            'author': idea_data.get('author', ''),
            'description': idea_data.get('description', ''),
            'category': category,
            'user_id': user_id
        }

        idea_id = firebase_manager.save_idea(idea_record)

        # إشعار المشرفين
        for admin_id in ADMIN_IDS:
            try:
                admin_text = f"""
💡 **فكرة جديدة مقترحة**

📝 **اسم الفكرة:** {idea_data.get('name', 'غير محدد')}
👤 **صاحب الفكرة:** {idea_data.get('author', 'غير محدد')}
📂 **القسم:** {category} (قسم جديد)
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📋 **الوصف:**
{idea_data.get('description', 'غير محدد')}

🆔 **معرف المستخدم:** {user_id}
"""
                bot.send_message(admin_id, admin_text, parse_mode='Markdown')
            except:
                pass

        # رسالة شكر للمستخدم
        success_text = f"""
✅ **تم إرسال فكرتك بنجاح!**

📝 **ملخص الفكرة:**
• الاسم: {idea_data.get('name', 'غير محدد')}
• صاحب الفكرة: {idea_data.get('author', 'غير محدد')}
• القسم: {category} (قسم جديد)
• رقم الفكرة: #{idea_id if idea_id else 'غير محدد'}

🔔 **سيتم مراجعة فكرتك من قبل الفريق المختص**
📧 **وسيتم التواصل معك في حالة الموافقة**

شكراً لمساهمتك في تطوير المنصة! 🙏
"""

        bot.send_message(
            message.chat.id,
            success_text,
            parse_mode='Markdown',
            reply_markup=create_main_keyboard(user_id)
        )

        set_user_state(user_id, UserStates.MAIN_MENU)

        # تنظيف بيانات المستخدم
        if 'idea' in user_data_dict:
            del user_data_dict['idea']
            save_user_data(user_id, user_data_dict)

    elif user_state == UserStates.ADMIN_BROADCAST:
        # إرسال رسالة جماعية (للمشرفين فقط)
        if user_id in ADMIN_IDS:
            try:
                # الحصول على قائمة جميع المستخدمين
                all_users = firebase_manager.get_all_users()
                sent_count = 0
                failed_count = 0

                broadcast_text = f"""
📢 **رسالة من إدارة البوت**

{text}

---
📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                for user in all_users:
                    try:
                        bot.send_message(user['user_id'], broadcast_text, parse_mode='Markdown')
                        sent_count += 1
                        time.sleep(0.1)  # تجنب حدود التيليجرام
                    except:
                        failed_count += 1

                result_text = f"""
✅ **تم إرسال الرسالة الجماعية!**

📊 **الإحصائيات:**
• تم الإرسال بنجاح: {sent_count}
• فشل الإرسال: {failed_count}
• المجموع: {sent_count + failed_count}

📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                bot.send_message(
                    message.chat.id,
                    result_text,
                    parse_mode='Markdown',
                    reply_markup=create_main_keyboard(user_id)
                )

                set_user_state(user_id, UserStates.MAIN_MENU)

            except Exception as e:
                logger.error(f"خطأ في إرسال الرسالة الجماعية: {e}")
                bot.send_message(
                    message.chat.id,
                    f"❌ خطأ في إرسال الرسالة الجماعية:\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
        else:
            bot.send_message(message.chat.id, "❌ غير مصرح لك بهذه العملية")

    else:
        # رسالة غير معروفة
        if user_state == UserStates.MAIN_MENU:
            unknown_text = """
🤔 **لم أفهم طلبك**

يرجى استخدام الأزرار الموجودة أدناه للتنقل في البوت.

إذا كنت تواجه مشكلة، يمكنك استخدام الأمر /help للحصول على المساعدة.
"""
            bot.send_message(
                message.chat.id,
                unknown_text,
                parse_mode='Markdown',
                reply_markup=create_main_keyboard(user_id)
            )
        else:
            bot.send_message(
                message.chat.id,
                "🔄 يرجى إكمال العملية الحالية أو العودة للقائمة الرئيسية",
                reply_markup=create_back_keyboard()
            )

# معالج الأخطاء
def error_handler(func):
    """معالج الأخطاء العام"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"خطأ في {func.__name__}: {e}")
            if args and hasattr(args[0], 'chat'):
                try:
                    bot.send_message(
                        args[0].chat.id,
                        "❌ حدث خطأ مؤقت. يرجى المحاولة مرة أخرى."
                    )
                except:
                    pass
    return wrapper

# تطبيق معالج الأخطاء على الوظائف الرئيسية
start_command = error_handler(start_command)
handle_callback_query = error_handler(handle_callback_query)
handle_text_messages = error_handler(handle_text_messages)

if __name__ == "__main__":
    logger.info("🚀 بدء تشغيل البوت التعليمي المحسن...")
    logger.info(f"📊 تم تحميل {len(educational_content)} مراحل دراسية")
    logger.info(f"🗂️ تم تحميل {len(ministerial_questions)} مواد وزارية")
    logger.info(f"🛠️ تم تحميل {len(services_data)} خدمات")
    logger.info(f"👨‍💼 تم تحميل {len(ADMIN_IDS)} مشرف")

    try:
        # إرسال رسالة للمشرفين عند بدء التشغيل
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"🚀 **تم بدء تشغيل البوت بنجاح!**\n\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n🔧 **لوحة التحكم متاحة الآن**",
                    parse_mode='Markdown'
                )
            except:
                pass

        logger.info("✅ البوت جاهز لاستقبال الرسائل")
        bot.polling(none_stop=True, interval=0, timeout=20)

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        # إرسال إشعار للمشرفين عن الخطأ
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"❌ **خطأ في البوت:**\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
            except:
                pass
